import { ApplyOptions } from '@sapphire/decorators';
import { HttpCodes, Route } from '@sapphire/plugin-api';
import crypto from 'crypto';
import { ChannelType, TextChannel } from 'discord.js';
import z from 'zod';

const WebhookSchema = z.object({
	message_id: z.string(),
	ticket_id: z.string(),
	message: z.string(),
	user_id: z.string(),
	user_name: z.string(),
	user_email: z.string(),
	contact_id: z.string(),
	contact_name: z.string(),
	contact_email: z.string().optional(),
	contact_identifier: z.string().optional(),
	channel_id: z.string()
});

@ApplyOptions<Route.Options>({ route: '/webhook', methods: ['POST'] })
export class WebhookRoute extends Route {
	public override async run(request: Route.Request, response: Route.Response): Promise<void> {
		const signature = request.headers['trengo-signature'] as string | undefined;

		if (!signature) {
			this.container.logger.warn('recv /webhook (missing signature)');
			return response.error(HttpCodes.Unauthorized);
		}

		const parts = signature.split(';');

		if (parts.length !== 2) {
			this.container.logger.warn('recv /webhook (invalid signature format)');
			return response.error(HttpCodes.Unauthorized);
		}

		const timestamp = parts[0];
		const hash = parts[1].toLowerCase();
		const secret = process.env.TRENGO_WEBHOOK_SECRET!;

		let body: string;
		let payload: z.infer<typeof WebhookSchema>;

		try {
			body = JSON.stringify(await request.readBodyJson());
		} catch (error) {
			this.container.logger.error('recv /webhook (failed to read body):', error);
			return response.error(HttpCodes.BadRequest);
		}

		try {
			payload = JSON.parse(body);
			this.container.logger.debug('recv /webhook (parsed body):', payload);
		} catch (error) {
			this.container.logger.error('recv /webhook (failed to parse body):', error);
			return response.error(HttpCodes.BadRequest);
		}

		const expected = crypto.createHmac('sha256', secret).update(`${timestamp}.${body}`).digest('hex').toLowerCase();

		if (expected !== hash) {
			this.container.logger.warn('recv /webhook (signature verification failed)', { expected, hash, timestamp });

			return response.error(HttpCodes.Unauthorized);
		}

		this.container.logger.info('recv /webhook (signature verified successfully)');

		if (payload.contact_identifier?.startsWith('custom-') && payload.message) {
			const id = this.container.trengo.fromIdentifier(payload.contact_identifier);

			let channel: TextChannel | undefined;

			if (this.container.tickets.has(id)) {
				channel = this.container.client.channels.cache.get(this.container.tickets.get(id)!) as TextChannel;
			} else {
				channel = this.container.client.channels.cache.find(
					(channel) => channel.type === ChannelType.GuildText && channel.name === id
				) as TextChannel;

				this.container.tickets.set(id, channel.id);
			}

			await channel?.send({ content: payload.message });
		}

		return response.ok(HttpCodes.Accepted);
	}
}
