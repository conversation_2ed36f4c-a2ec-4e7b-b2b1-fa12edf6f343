import { ApplyOptions } from '@sapphire/decorators';
import { InteractionHandler, InteractionHandlerTypes } from '@sapphire/framework';
import { ChannelType, MessageFlags, TextChannel, type ButtonInteraction } from 'discord.js';
import { SupportEmbed } from '../components/embeds/support';
import { messages } from '../components/messages';

@ApplyOptions<InteractionHandler.Options>({
	interactionHandlerType: InteractionHandlerTypes.Button
})
export class Button<PERSON>and<PERSON> extends InteractionHandler {
	public async run(interaction: ButtonInteraction) {
		let channel = interaction.guild?.channels.cache.find((channel) => channel.name === interaction.user.id) as TextChannel | undefined;

		if (channel === undefined) {
			channel = (await interaction.guild?.channels.create({
				topic: this.container.trengo.toIdentifier(interaction.user.id),
				parent: (interaction.channel as TextChannel).parentId,
				name: interaction.user.id,
				type: ChannelType.GuildText
			})) as TextChannel;

			await channel.permissionOverwrites.edit(interaction.guild?.id!, {
				ViewChannel: false,
				SendMessages: true,
				EmbedLinks: true,
				AttachFiles: true,
				AddReactions: true,
				UseExternalEmojis: true,
				SendVoiceMessages: true,
				ReadMessageHistory: true
			});

			await channel.permissionOverwrites.edit(interaction.user.id, { ViewChannel: true });
		}

		this.container.tickets.set(channel.name, channel.id);

		await channel.send({ embeds: [SupportEmbed] });

		if (!channel.topic) {
			await channel.setTopic(this.container.trengo.toIdentifier(interaction.user.id));
		}

		const response = await this.container.trengo.send({
			contact: { name: interaction.user.username, identifier: channel.topic! },
			content: messages.system.ticket.creation
		});

		if (response.status === 200) {
			return interaction.reply({ content: messages.ticket.confirmation, flags: MessageFlags.Ephemeral });
		}

		this.container.logger.error(JSON.stringify(response, null, 2));

		return interaction.reply({ content: 'something went wrong!', flags: MessageFlags.Ephemeral });
	}

	public override parse(interaction: ButtonInteraction) {
		if (interaction.customId.startsWith('ticket-button')) {
		}

		return this.some();
	}
}
