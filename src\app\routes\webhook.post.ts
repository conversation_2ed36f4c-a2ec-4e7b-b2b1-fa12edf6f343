import { ApplyOptions } from '@sapphire/decorators';
import { HttpCodes, Route } from '@sapphire/plugin-api';
import { ChannelType, TextChannel } from 'discord.js';
import crypto.
import z from 'zod';

const WebhookSchema = z.object({
	message_id: z.string(),
	ticket_id: z.string(),
	message: z.string(),
	user_id: z.string(),
	user_name: z.string(),
	user_email: z.string(),
	contact_id: z.string(),
	contact_name: z.string(),
	contact_email: z.string().nullable(),
	contact_identifier: z.string().nullable(),
	channel_id: z.string()
});

@ApplyOptions<Route.Options>({ route: '/webhook', methods: ['POST'] })
export class WebhookRoute extends Route {
	public override async run(request: Route.Request, response: Route.Response): Promise<void> {
		const signature = request.headers['trengo-signature'] as string | undefined;

		if (!signature) {
			this.container.logger.warn('recv /webhook (missing signature)');
			return response.error(HttpCodes.Unauthorized);
		}

		const payload = (await request.readBodyJson()) as z.infer<typeof WebhookSchema>;

		const secret = process.env.TRENGO_WEBHOOK_SECRET!;
		const parts = signature.split(';');
		const timestamp = parts[0];
		const hash = parts[1].toLowerCase();

		const hex = crypto
			.createHmac('sha256', secret)
			.update(`${timestamp}.${JSON.stringify(payload)}`)
			.digest('hex')
			.toLowerCase();

		if (hex !== hash) {
			this.container.logger.warn('recv /webhook (unverified signature)');
			return response.error(HttpCodes.Unauthorized);
		}

		this.container.logger.debug(JSON.stringify(payload, null, 2));

		if (payload.contact_identifier?.startsWith('custom-') && payload.message) {
			const id = this.container.trengo.fromIdentifier(payload.contact_identifier);

			let channel: TextChannel | undefined;

			if (this.container.tickets.has(id)) {
				channel = this.container.client.channels.cache.get(this.container.tickets.get(id)!) as TextChannel;
			} else {
				channel = this.container.client.channels.cache.find(
					(channel) => channel.type === ChannelType.GuildText && channel.name === id
				) as TextChannel;

				this.container.tickets.set(id, channel.id);
			}

			await channel?.send({ content: payload.message });
		}

		return response.ok(HttpCodes.Accepted);
	}
}
